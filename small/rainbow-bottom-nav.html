<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>彩虹渐变底部导航栏</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(45deg, #ff9a9e 0%, #fecfef 25%, #fecfef 50%, #fecfef 75%, #fecfef 100%);
            min-height: 100vh;
            padding-bottom: 80px;
        }

        .main-content {
            padding: 40px 20px;
            min-height: calc(100vh - 80px);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .main-content h1 {
            font-size: 2.8rem;
            background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
            background-size: 400% 400%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
            font-weight: 700;
            animation: gradientText 4s ease infinite;
        }

        @keyframes gradientText {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .main-content p {
            font-size: 1.2rem;
            color: #555;
            max-width: 600px;
            line-height: 1.8;
            transition: opacity 0.3s ease;
        }

        /* 彩虹渐变底部导航栏 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: linear-gradient(45deg, 
                #ff6b6b 0%, 
                #4ecdc4 20%, 
                #45b7d1 40%, 
                #96ceb4 60%, 
                #feca57 80%, 
                #ff9ff3 100%);
            background-size: 300% 300%;
            display: flex;
            justify-content: space-around;
            align-items: center;
            z-index: 1000;
            box-shadow: 0 -10px 40px rgba(0, 0, 0, 0.2);
            animation: rainbowShift 6s ease infinite;
        }

        @keyframes rainbowShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* 添加彩虹光效 */
        .bottom-nav::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, 
                rgba(255, 255, 255, 0.3) 0%, 
                transparent 30%, 
                rgba(255, 255, 255, 0.1) 60%,
                transparent 100%);
            pointer-events: none;
            animation: shimmer 3s ease infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            flex: 1;
            height: 100%;
            text-decoration: none;
            color: rgba(255, 255, 255, 0.8);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            border-radius: 20px;
            margin: 10px 6px;
            backdrop-filter: blur(15px);
        }

        .nav-item.active {
            color: #fff;
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }

        .nav-item:hover:not(.active) {
            color: #fff;
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        .nav-icon {
            width: 28px;
            height: 28px;
            margin-bottom: 6px;
            fill: currentColor;
            transition: all 0.4s ease;
        }

        .nav-item.active .nav-icon {
            transform: scale(1.2) rotate(5deg);
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
        }

        .nav-text {
            font-size: 12px;
            line-height: 1;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        /* 活跃状态的彩虹光环 */
        .nav-item.active::after {
            content: '';
            position: absolute;
            top: -3px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 4px;
            background: linear-gradient(90deg, 
                transparent, 
                #fff 20%, 
                #ff6b6b 40%, 
                #4ecdc4 60%, 
                #fff 80%, 
                transparent);
            border-radius: 2px;
            opacity: 0.9;
            animation: glow 2s ease infinite;
        }

        @keyframes glow {
            0%, 100% { opacity: 0.9; }
            50% { opacity: 0.6; }
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .bottom-nav {
                height: 70px;
            }
            
            .nav-text {
                font-size: 10px;
            }
            
            .nav-icon {
                width: 24px;
                height: 24px;
            }
            
            .nav-item {
                margin: 8px 3px;
                border-radius: 16px;
            }
            
            .main-content h1 {
                font-size: 2.2rem;
            }
            
            .main-content p {
                font-size: 1rem;
            }
        }

        /* 添加浮动粒子效果 */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0; }
            50% { transform: translateY(-100px) rotate(180deg); opacity: 1; }
        }
    </style>
</head>
<body>
    <!-- 浮动粒子背景 -->
    <div class="particles" id="particles"></div>

    <div class="main-content">
        <h1 id="page-title">首页</h1>
        <p id="page-description">欢迎来到彩虹渐变底部导航栏示例。体验绚丽的彩虹色彩和流畅的动画效果。</p>
    </div>

    <!-- 彩虹渐变底部导航栏 -->
    <nav class="bottom-nav">
        <a href="#home" class="nav-item active" data-page="home">
            <svg class="nav-icon" viewBox="0 0 1024 1024">
                <path d="M946.5 505L560.1 118.8l-25.9-25.9c-12.3-12.2-32.1-12.2-44.4 0L77.5 505c-12.3 12.3-18.9 28.6-18.9 46 0 35.3 28.7 64 64 64h43.4V908c0 17.7 14.3 32 32 32H448V716h112v224h265.9c17.7 0 32-14.3 32-32V615h43.4c17 0 33.3-6.7 45.3-18.8 24.9-25 24.9-65.5-.1-90.2z"/>
            </svg>
            <span class="nav-text">首页</span>
        </a>

        <a href="#workspace" class="nav-item" data-page="workspace">
            <svg class="nav-icon" viewBox="0 0 1024 1024">
                <path d="M924.8 385.6c-22.6-53.4-54.9-101.3-96-142.4s-89-73.4-142.4-96C631.1 123.8 572.5 112 512 112s-119.1 11.8-174.4 35.2c-53.4 22.6-101.3 54.9-142.4 96s-73.4 89-96 142.4C75.8 440.9 64 499.5 64 560s11.8 119.1 35.2 174.4c22.6 53.4 54.9 101.3 96 142.4s89 73.4 142.4 96C440.9 996.2 499.5 1008 560 1008s119.1-11.8 174.4-35.2c53.4-22.6 101.3-54.9 142.4-96s73.4-89 96-142.4C996.2 679.1 1008 620.5 1008 560s-11.8-119.1-35.2-174.4z"/>
            </svg>
            <span class="nav-text">工作台</span>
        </a>

        <a href="#orders" class="nav-item" data-page="orders">
            <svg class="nav-icon" viewBox="0 0 1024 1024">
                <path d="M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zM760 888H264V136h496v752z"/>
                <path d="M304 230.4h416c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H304c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"/>
            </svg>
            <span class="nav-text">订单</span>
        </a>

        <a href="#cart" class="nav-item" data-page="cart">
            <svg class="nav-icon" viewBox="0 0 1024 1024">
                <path d="M922.9 701.9H327.4l29.9-60.9 496.8-.9c16.8 0 31.2-12 34.2-28.6l68.8-385.1c1.8-10.1-.9-20.5-7.5-28.4s-16.3-12.2-26.6-12.2H218.4l-6.7-34.9C208.7 127.3 186.1 112 160.4 112H96c-17.7 0-32 14.3-32 32s14.3 32 32 32h64.4l35.9 187.7c.4 2.4 1.1 4.7 2.1 6.9l50.8 105c2.8 5.7 8.6 9.3 15 9.3h535.8c17.7 0 32-14.3 32-32 .1-17.7-14.2-32-31.9-32z"/>
            </svg>
            <span class="nav-text">购物车</span>
        </a>

        <a href="#profile" class="nav-item" data-page="profile">
            <svg class="nav-icon" viewBox="0 0 1024 1024">
                <path d="M858.5 763.6c-18.9-44.8-46.1-85-80.6-119.5-34.5-34.5-74.7-61.6-119.5-80.6-0.4-0.2-0.8-0.3-1.2-0.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-0.4 0.2-0.8 0.3-1.2 0.5-44.8 18.9-85 46.1-119.5 80.6-34.5 34.5-61.6 74.7-80.6 119.5C146.9 807.5 137 854 136 901.8c-0.1 4.5 3.5 8.2 8 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c0.1 4.4 3.6 7.8 8 7.8h60c4.5 0 8.1-3.7 8-8.2-1-47.8-10.9-94.3-29.5-138.2z"/>
            </svg>
            <span class="nav-text">我的</span>
        </a>
    </nav>

    <script>
        // 创建浮动粒子
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 20;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // 创建粒子效果
            createParticles();

            const navItems = document.querySelectorAll('.nav-item');
            const pageTitle = document.getElementById('page-title');
            const pageDescription = document.getElementById('page-description');

            const pageContent = {
                'home': {
                    title: '首页',
                    description: '欢迎来到彩虹渐变底部导航栏示例。体验绚丽的彩虹色彩和流畅的动画效果。'
                },
                'workspace': {
                    title: '工作台',
                    description: '这是工作台页面，彩虹色彩为您的工作环境带来活力和创意灵感。'
                },
                'orders': {
                    title: '订单',
                    description: '订单管理页面，在彩虹般绚丽的界面中管理您的所有订单信息。'
                },
                'cart': {
                    title: '购物车',
                    description: '购物车页面，彩虹渐变让您的购物体验更加愉悦和充满乐趣。'
                },
                'profile': {
                    title: '我的',
                    description: '个人中心页面，在彩虹色彩的包围中管理您的个人信息和设置。'
                }
            };

            navItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();

                    // 移除所有活跃状态
                    navItems.forEach(nav => nav.classList.remove('active'));

                    // 添加当前项的活跃状态
                    this.classList.add('active');

                    // 更新页面内容
                    const page = this.getAttribute('data-page');
                    const content = pageContent[page];

                    // 添加页面切换动画
                    pageTitle.style.opacity = '0';
                    pageDescription.style.opacity = '0';

                    setTimeout(() => {
                        pageTitle.textContent = content.title;
                        pageDescription.textContent = content.description;
                        pageTitle.style.opacity = '1';
                        pageDescription.style.opacity = '1';
                    }, 200);
                });
            });
        });
    </script>
</body>
</html>
