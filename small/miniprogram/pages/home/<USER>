// 首页逻辑
Page({
  data: {
    tableNumber: 0, // 桌号（数字类型）
    manualTableNumber: '', // 手动输入的桌号
    // 桌号列表（1-12号桌）
    tableList: [
      { number: 1, occupied: false },
      { number: 2, occupied: true },  // 示例：2号桌已占用
      { number: 3, occupied: false },
      { number: 4, occupied: false },
      { number: 5, occupied: true },  // 示例：5号桌已占用
      { number: 6, occupied: false },
      { number: 7, occupied: false },
      { number: 8, occupied: false },
      { number: 9, occupied: false },
      { number: 10, occupied: false },
      { number: 11, occupied: false },
      { number: 12, occupied: false }
    ],

    // 店铺信息
    shopInfo: {
      name: '川味小厨',
      description: '正宗川菜 · 地道美味',
      phone: '023-8888-6666',
      address: '重庆市渝中区美食街88号',
      businessHours: '09:00 - 22:00',
      logo: '/images/logo.png'
    },

    // 今日推荐菜品
    recommendList: [
      {
        id: 1,
        name: '宫保鸡丁',
        price: 28,
        emoji: '🍗',
        description: '经典川菜，鸡肉嫩滑，花生香脆',
        image: '/images/dishes/gongbao.jpg'
      },
      {
        id: 2,
        name: '麻婆豆腐',
        price: 18,
        emoji: '🥘',
        description: '嫩滑豆腐配麻辣肉末，口感丰富',
        image: '/images/dishes/mapo.jpg'
      },
      {
        id: 3,
        name: '酸辣土豆丝',
        price: 12,
        emoji: '🥔',
        description: '爽脆土豆丝，酸辣开胃',
        image: '/images/dishes/tudousi.jpg'
      },
      {
        id: 4,
        name: '水煮鱼',
        price: 45,
        emoji: '🐟',
        description: '鲜嫩鱼片，麻辣鲜香',
        image: '/images/dishes/shuizhuyu.jpg'
      }
    ]
  },

  onLoad() {
    console.log('首页加载完成')

    // 检查是否已经选择过桌号
    const savedTableNumber = wx.getStorageSync('tableNumber')
    if (savedTableNumber) {
      this.setData({
        tableNumber: parseInt(savedTableNumber) || 0
      })
    }

    // 检查是否有当前订单
    this.checkCurrentOrder()
  },

  onShow() {
    // 每次显示页面时检查订单状态
    this.checkCurrentOrder()
  },

  // 检查当前订单状态
  checkCurrentOrder() {
    const orders = wx.getStorageSync('orders') || []
    if (orders.length > 0) {
      const latestOrder = orders[orders.length - 1]
      const now = Date.now()
      const elapsed = now - latestOrder.createTime

      // 如果最新订单在20分钟内，显示订单状态
      if (elapsed < 20 * 60 * 1000) {
        let status = '待处理'
        if (elapsed > 5 * 60 * 1000) {
          status = '制作中'
        }

        wx.showToast({
          title: `您的订单${status}`,
          icon: 'none',
          duration: 2000
        })
      }
    }
  },



  // 选择桌号
  selectTable(e: any) {
    const tableNumber = parseInt(e.currentTarget.dataset.table)
    const occupied = e.currentTarget.dataset.occupied

    // 如果桌子已被占用，提示用户
    if (occupied) {
      wx.showToast({
        title: `${tableNumber}号桌已被占用`,
        icon: 'error'
      })
      return
    }

    this.setData({
      tableNumber: tableNumber
    })
    wx.setStorageSync('tableNumber', tableNumber.toString())
    wx.showToast({
      title: `已选择${tableNumber}号桌`,
      icon: 'success'
    })
  },

  // 手动输入桌号
  onManualTableInput(e: any) {
    this.setData({
      manualTableNumber: e.detail.value
    })
  },

  // 确认手动输入
  confirmManualInput() {
    const { manualTableNumber } = this.data
    if (!manualTableNumber.trim()) {
      wx.showToast({
        title: '请输入桌号',
        icon: 'error'
      })
      return
    }

    const tableNumber = parseInt(manualTableNumber.trim()) || 0
    this.setData({
      tableNumber: tableNumber,
      manualTableNumber: ''
    })
    wx.setStorageSync('tableNumber', tableNumber.toString())
    wx.showToast({
      title: `已选择${tableNumber}号桌`,
      icon: 'success'
    })
  },

  // 桌号输入（保留兼容性）
  onTableNumberInput(e: any) {
    const tableNumber = parseInt(e.detail.value) || 0
    this.setData({
      tableNumber: tableNumber
    })
    // 保存到本地存储
    if (tableNumber) {
      wx.setStorageSync('tableNumber', tableNumber.toString())
    }
  },

  // 开始点餐
  startOrder() {
    const { tableNumber } = this.data

    if (!tableNumber) {
      wx.showToast({
        title: '请先选择桌号',
        icon: 'error'
      })
      return
    }

    // 保存桌号信息
    wx.setStorageSync('tableNumber', tableNumber.toString())

    // 跳转到菜单页
    wx.switchTab({
      url: '/pages/menu/menu'
    })

    wx.showToast({
      title: `${tableNumber}号桌，开始点餐`,
      icon: 'success'
    })
  },

  /**
   * 底部导航栏切换事件
   */
  onTabChange(e: any) {
    console.log('Tab changed:', e.detail);
    // 这里可以添加额外的逻辑，比如数据统计等
  }
})
