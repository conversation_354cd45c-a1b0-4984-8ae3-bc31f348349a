<!DOCTYPE html>
<html>
<head>
    <title>生成小程序图标</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .icon-container { margin: 20px 0; }
        canvas { border: 1px solid #ccc; margin: 10px; }
        button { padding: 10px 20px; margin: 5px; background: #FF6B35; color: white; border: none; border-radius: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>小程序图标生成器</h1>
    <p>点击下载按钮保存图标到 miniprogram/images/ 目录</p>
    
    <div class="icon-container">
        <h3>首页图标</h3>
        <canvas id="home" width="64" height="64"></canvas>
        <canvas id="home-active" width="64" height="64"></canvas>
        <button onclick="downloadIcon('home')">下载 home.png</button>
        <button onclick="downloadIcon('home-active')">下载 home-active.png</button>
    </div>
    
    <div class="icon-container">
        <h3>菜单图标</h3>
        <canvas id="menu" width="64" height="64"></canvas>
        <canvas id="menu-active" width="64" height="64"></canvas>
        <button onclick="downloadIcon('menu')">下载 menu.png</button>
        <button onclick="downloadIcon('menu-active')">下载 menu-active.png</button>
    </div>
    
    <div class="icon-container">
        <h3>个人图标</h3>
        <canvas id="profile" width="64" height="64"></canvas>
        <canvas id="profile-active" width="64" height="64"></canvas>
        <button onclick="downloadIcon('profile')">下载 profile.png</button>
        <button onclick="downloadIcon('profile-active')">下载 profile-active.png</button>
    </div>

    <script>
        // 绘制首页图标 (房子)
        function drawHome(ctx, active = false) {
            const color = active ? '#FF6B35' : '#999999';
            ctx.fillStyle = color;
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            
            // 房子轮廓
            ctx.beginPath();
            ctx.moveTo(32, 15);
            ctx.lineTo(15, 30);
            ctx.lineTo(20, 30);
            ctx.lineTo(20, 50);
            ctx.lineTo(44, 50);
            ctx.lineTo(44, 30);
            ctx.lineTo(49, 30);
            ctx.closePath();
            ctx.fill();
            
            // 门
            ctx.fillStyle = active ? '#ffffff' : '#f0f0f0';
            ctx.fillRect(28, 38, 8, 12);
        }
        
        // 绘制菜单图标 (三条线)
        function drawMenu(ctx, active = false) {
            const color = active ? '#FF6B35' : '#999999';
            ctx.fillStyle = color;
            
            // 三条横线
            ctx.fillRect(16, 20, 32, 4);
            ctx.fillRect(16, 30, 32, 4);
            ctx.fillRect(16, 40, 32, 4);
        }
        
        // 绘制个人图标 (人形)
        function drawProfile(ctx, active = false) {
            const color = active ? '#FF6B35' : '#999999';
            ctx.fillStyle = color;
            
            // 头部 (圆形)
            ctx.beginPath();
            ctx.arc(32, 22, 8, 0, 2 * Math.PI);
            ctx.fill();
            
            // 身体 (梯形)
            ctx.beginPath();
            ctx.moveTo(20, 50);
            ctx.lineTo(24, 35);
            ctx.lineTo(40, 35);
            ctx.lineTo(44, 50);
            ctx.closePath();
            ctx.fill();
        }
        
        // 初始化画布
        function initCanvas() {
            // 首页图标
            drawHome(document.getElementById('home').getContext('2d'), false);
            drawHome(document.getElementById('home-active').getContext('2d'), true);
            
            // 菜单图标
            drawMenu(document.getElementById('menu').getContext('2d'), false);
            drawMenu(document.getElementById('menu-active').getContext('2d'), true);
            
            // 个人图标
            drawProfile(document.getElementById('profile').getContext('2d'), false);
            drawProfile(document.getElementById('profile-active').getContext('2d'), true);
        }
        
        // 下载图标
        function downloadIcon(id) {
            const canvas = document.getElementById(id);
            const link = document.createElement('a');
            link.download = id + '.png';
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // 页面加载完成后初始化
        window.onload = initCanvas;
    </script>
</body>
</html>
